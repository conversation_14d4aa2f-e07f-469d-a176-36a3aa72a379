<script setup lang="ts">
import { ref, onMounted } from 'vue'
import Food from '../components/Food.vue'
import { foodMenuService, type MenuData } from '../services/api'

const foodData = ref<MenuData | null>(null)
const selectedDate = ref('')
const loading = ref(false)
const error = ref('')

// 初始化日期为今天
const initDate = () => {
  selectedDate.value = foodMenuService.getTodayDate()
}

// 获取菜谱数据
const fetchFoodData = async (date: string) => {
  loading.value = true
  error.value = ''

  try {
    const data = await foodMenuService.getMenuData(date)
    foodData.value = data
  } catch (err) {
    console.error('获取菜谱数据失败:', err)
    error.value = '获取菜谱数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 处理日期变化
const handleDateChange = () => {
  if (selectedDate.value) {
    fetchFoodData(selectedDate.value)
  }
}

// 组件挂载时初始化
onMounted(() => {
  initDate()
  fetchFoodData(selectedDate.value)
})
</script>

<template>
  <div class="dinner-page">
    <div class="page-header">
      <h1 class="page-title">晚餐菜谱</h1>
      <div class="date-selector">
        <label for="date-input">选择日期：</label>
        <input
          id="date-input"
          type="date"
          v-model="selectedDate"
          @change="handleDateChange"
          class="date-input"
        />
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      <p>正在加载菜谱数据...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error">
      <p>{{ error }}</p>
      <button @click="handleDateChange" class="retry-btn">重试</button>
    </div>

    <!-- 菜谱内容 -->
    <div v-else>
      <Food :FoodData="foodData" mealType="DINNER" />
    </div>
  </div>
</template>

<style scoped>
.dinner-page {
  min-height: 100vh;
  background-color: #f8f9fa;
}

.page-header {
  background: linear-gradient(135deg, #d1a3ff, #a29bfe);
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 0 0 20px 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.page-title {
  margin: 0 0 15px 0;
  color: #2d3436;
  font-size: 28px;
  font-weight: bold;
  text-align: center;
}

.date-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.date-selector label {
  color: #2d3436;
  font-weight: 500;
}

.date-input {
  padding: 8px 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  background: white;
  transition: border-color 0.2s ease;
}

.date-input:focus {
  outline: none;
  border-color: #a29bfe;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  margin: 20px;
  border-radius: 8px;
}

.loading {
  background: #e3f2fd;
  color: #1976d2;
}

.error {
  background: #ffebee;
  color: #c62828;
}

.retry-btn {
  margin-top: 10px;
  padding: 8px 16px;
  background: #c62828;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-btn:hover {
  background: #b71c1c;
}

@media (max-width: 768px) {
  .page-header {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .date-selector {
    flex-direction: column;
    gap: 8px;
  }
}
</style>