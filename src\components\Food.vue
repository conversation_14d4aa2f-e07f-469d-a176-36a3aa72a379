<script setup lang="ts">
import { ref, computed } from 'vue'

interface FoodItem {
  name: string
  price: string
  img?: string
  subTitle?: string
  intro?: string
}

interface FoodCategory {
  category: string
  img?: string
  isTop?: boolean
  datas: FoodItem[]
}

interface TimeInterval {
  id: number
  name: string
  type: string
  period: string
  startTime: string
  endTime: string
}

interface FoodData {
  date?: string
  displayDate?: string
  canteenName?: string
  canteenLogo?: string
  currentTimeInterval?: TimeInterval
  mealTimeIntervals?: TimeInterval[]
  menu?: {
    mealTypeAndCategoryFoods?: Record<string, FoodCategory[]>
  }
}

const props = defineProps<{
  FoodData: FoodData
  mealType?: string // 'BREAKFAST', 'LUNCH', 'DINNER'
}>()

// 根据餐次类型获取对应的菜谱数据
const currentMealData = computed(() => {
  if (!props.FoodData?.menu?.mealTypeAndCategoryFoods) return []

  // 如果指定了餐次类型，查找对应的时间段ID
  if (props.mealType && props.FoodData.mealTimeIntervals) {
    const timeInterval = props.FoodData.mealTimeIntervals.find(
      interval => interval.type === props.mealType
    )
    if (timeInterval) {
      return props.FoodData.menu.mealTypeAndCategoryFoods[timeInterval.id.toString()] || []
    }
  }

  // 否则使用当前时间段
  const currentId = props.FoodData.currentTimeInterval?.id
  if (currentId) {
    return props.FoodData.menu.mealTypeAndCategoryFoods[currentId.toString()] || []
  }

  return []
})

// 获取当前餐次信息
const currentMealInfo = computed(() => {
  if (props.mealType && props.FoodData?.mealTimeIntervals) {
    return props.FoodData.mealTimeIntervals.find(
      interval => interval.type === props.mealType
    )
  }
  return props.FoodData?.currentTimeInterval
})
</script>

<template>
  <div class="food-container">
    <!-- 食堂信息头部 -->
    <div class="canteen-header" v-if="FoodData.canteenName">
      <div class="canteen-info">
        <img
          v-if="FoodData.canteenLogo"
          :src="FoodData.canteenLogo"
          :alt="FoodData.canteenName"
          class="canteen-logo"
        />
        <div class="canteen-details">
          <h1 class="canteen-name">{{ FoodData.canteenName }}</h1>
          <p class="date-info">{{ FoodData.displayDate }}</p>
        </div>
      </div>
    </div>

    <!-- 餐次信息 -->
    <div class="meal-info" v-if="currentMealInfo">
      <div class="meal-header">
        <h2 class="meal-name">{{ currentMealInfo.name }}</h2>
        <span class="meal-time">{{ currentMealInfo.period }}</span>
      </div>
    </div>

    <!-- 菜谱列表 -->
    <div class="menu-content" v-if="currentMealData.length > 0">
      <div
        v-for="category in currentMealData"
        :key="category.category"
        class="food-category"
      >
        <h3 class="category-title">{{ category.category }}</h3>
        <div class="food-grid">
          <div
            v-for="food in category.datas"
            :key="food.name"
            class="food-item"
          >
            <div class="food-info">
              <h4 class="food-name">{{ food.name }}</h4>
              <p v-if="food.subTitle" class="food-subtitle">{{ food.subTitle }}</p>
              <p v-if="food.intro" class="food-intro">{{ food.intro }}</p>
            </div>
            <div class="food-price">¥{{ food.price }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="no-data">
      <p>暂无菜谱数据</p>
    </div>
  </div>
</template>

<style scoped>
.food-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.canteen-header {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  color: white;
}

.canteen-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.canteen-logo {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
}

.canteen-name {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
}

.date-info {
  margin: 5px 0 0 0;
  opacity: 0.9;
  font-size: 16px;
}

.meal-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.meal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.meal-name {
  margin: 0;
  color: #2d3436;
  font-size: 20px;
}

.meal-time {
  background: #74b9ff;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.food-category {
  margin-bottom: 30px;
}

.category-title {
  color: #2d3436;
  font-size: 18px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid #ddd;
}

.food-grid {
  display: grid;
  gap: 12px;
}

.food-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.2s ease;
}

.food-item:hover {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transform: translateY(-1px);
}

.food-info {
  flex: 1;
}

.food-name {
  margin: 0 0 4px 0;
  color: #2d3436;
  font-size: 16px;
  font-weight: 500;
}

.food-subtitle {
  margin: 0 0 4px 0;
  color: #636e72;
  font-size: 14px;
}

.food-intro {
  margin: 0;
  color: #636e72;
  font-size: 12px;
}

.food-price {
  color: #e17055;
  font-size: 16px;
  font-weight: bold;
  margin-left: 15px;
}

.no-data {
  text-align: center;
  padding: 40px;
  color: #636e72;
}

@media (max-width: 768px) {
  .food-container {
    padding: 15px;
  }

  .canteen-info {
    flex-direction: column;
    text-align: center;
  }

  .meal-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .food-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .food-price {
    margin-left: 0;
    align-self: flex-end;
  }
}
</style>