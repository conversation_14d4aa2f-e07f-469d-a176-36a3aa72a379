import axios from 'axios'

// API配置
const API_CONFIG = {
  // 开发和生产环境都使用代理，避免CORS问题
  baseURL: '/api',
  timeout: 10000,
  canteenCode: 'WH_000'
}

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API请求:', config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.data)
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

// 数据类型定义
export interface FoodItem {
  name: string
  price: string
  img?: string
  subTitle?: string
  intro?: string
}

export interface FoodCategory {
  category: string
  img?: string
  isTop?: boolean
  datas: FoodItem[]
}

export interface TimeInterval {
  id: number
  canteenId: number
  name: string
  startTime: string
  endTime: string
  switchStartTime: string
  switchEndTime: string
  enable: boolean
  type: 'BREAKFAST' | 'LUNCH' | 'DINNER'
  priority: number
  period: string
  switchPeriod: string
  editable: boolean
  deletable: boolean
}

export interface MenuData {
  date: string
  canteenName: string
  canteenLogo?: string
  shareImgUrl?: string
  currentTimeInterval: TimeInterval
  displayDate: string
  mealTimeIntervals: TimeInterval[]
  menu: {
    id: number | null
    canteenId: number
    date: string
    dayOfWeek: number
    online: boolean
    title: string
    cover: string
    mealMenuFoods: any[]
    mealTimeTypeFoods: Record<string, any>
    mealFoods: any[]
    mealTypeAndCategoryFoods: Record<string, FoodCategory[]>
  }
  slogan?: string
  banners: any[]
}

export interface ApiResponse<T> {
  success: boolean
  msg: string
  code: number
  codeName: string
  data: T
}

// API服务类
class FoodMenuService {
  /**
   * 获取菜谱数据
   * @param date 日期 (YYYY-MM-DD格式)
   * @returns Promise<MenuData>
   */
  async getMenuData(date: string): Promise<MenuData> {
    const url = `/mealMenu/detail/h5?canteenCode=${API_CONFIG.canteenCode}&date=${date}`
    console.log('🚀 开始请求远程API:', API_CONFIG.baseURL + url)

    // 直接请求远程API，不使用本地数据降级
    const response = await apiClient.get<ApiResponse<MenuData>>(url)

    console.log('✅ 远程API请求成功:', response.data)

    if (response.data && response.data.success && response.data.data) {
      console.log('✅ 返回远程API数据')
      return response.data.data
    } else {
      throw new Error('API返回数据格式错误')
    }
  }



  /**
   * 根据餐次类型获取菜谱数据
   * @param date 日期
   * @param mealType 餐次类型
   * @returns Promise<FoodCategory[]>
   */
  async getMealData(date: string, mealType: 'BREAKFAST' | 'LUNCH' | 'DINNER'): Promise<FoodCategory[]> {
    const menuData = await this.getMenuData(date)

    if (!menuData.menu?.mealTypeAndCategoryFoods) {
      return []
    }

    // 查找对应餐次的时间段ID
    const timeInterval = menuData.mealTimeIntervals.find(
      interval => interval.type === mealType
    )

    if (!timeInterval) {
      return []
    }

    return menuData.menu.mealTypeAndCategoryFoods[timeInterval.id.toString()] || []
  }

  /**
   * 获取今日推荐菜品
   * @param date 日期
   * @param count 推荐数量
   * @returns Promise<FoodItem[]>
   */
  async getTodayRecommendations(date: string, count: number = 6): Promise<FoodItem[]> {
    const menuData = await this.getMenuData(date)
    const recommendations: FoodItem[] = []

    const mealData = menuData.menu?.mealTypeAndCategoryFoods
    if (mealData) {
      // 从所有餐次中收集菜品
      Object.values(mealData).forEach((categories: FoodCategory[]) => {
        categories.forEach((category) => {
          if (category.datas && category.datas.length > 0) {
            recommendations.push(...category.datas)
          }
        })
      })
    }

    // 随机选择指定数量的推荐菜品
    const shuffled = recommendations.sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  /**
   * 获取当前应该显示的餐次
   * @returns 当前餐次信息
   */
  getCurrentMealType(): { type: 'BREAKFAST' | 'LUNCH' | 'DINNER', name: string, icon: string, route: string } {
    const hour = new Date().getHours()

    if (hour >= 6 && hour < 10) {
      return { type: 'BREAKFAST', name: '早餐', icon: '🌅', route: '/breakfast' }
    } else if (hour >= 10 && hour < 16) {
      return { type: 'LUNCH', name: '午餐', icon: '☀️', route: '/lunch' }
    } else {
      return { type: 'DINNER', name: '晚餐', icon: '🌙', route: '/dinner' }
    }
  }

  /**
   * 格式化日期为API所需格式
   * @param date Date对象或日期字符串
   * @returns YYYY-MM-DD格式的日期字符串
   */
  formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toISOString().split('T')[0]
  }

  /**
   * 获取今天的日期
   * @returns 今天的日期字符串
   */
  getTodayDate(): string {
    return this.formatDate(new Date())
  }
}

// 导出服务实例
export const foodMenuService = new FoodMenuService()

// 导出默认实例
export default foodMenuService
