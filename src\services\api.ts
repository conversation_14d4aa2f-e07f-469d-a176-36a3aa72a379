import axios from 'axios'

// 本地备用数据
const localData = {
  data: {
    date: "2025-07-08",
    canteenName: "武汉金山软件园食堂",
    canteenLogo: "https://zhweb.kingsoft.com/imgs/202210/1024159A75134CB9A048FA1352635773.png",
    shareImgUrl: "https://zhweb.kingsoft.com/imgs/202210/1024159A75134CB9A048FA1352635773.png",
    currentTimeInterval: {
      id: 8,
      canteenId: 4,
      name: "早餐",
      startTime: "08:00",
      endTime: "09:30",
      switchStartTime: "06:00",
      switchEndTime: "09:45",
      enable: true,
      type: "BREAKFAST",
      priority: 0,
      period: "08:00 - 09:30",
      switchPeriod: "06:00 - 09:45",
      editable: true,
      deletable: true
    },
    displayDate: "7月8日",
    mealTimeIntervals: [
      {
        id: 8,
        canteenId: 4,
        name: "早餐",
        startTime: "08:00",
        endTime: "09:30",
        switchStartTime: "06:00",
        switchEndTime: "09:45",
        enable: true,
        type: "BREAKFAST",
        priority: 0,
        period: "08:00 - 09:30",
        switchPeriod: "06:00 - 09:45",
        editable: true,
        deletable: true
      },
      {
        id: 9,
        canteenId: 4,
        name: "午餐",
        startTime: "11:40",
        endTime: "13:30",
        switchStartTime: "09:46",
        switchEndTime: "14:00",
        enable: true,
        type: "LUNCH",
        priority: 0,
        period: "11:40 - 13:30",
        switchPeriod: "09:46 - 14:00",
        editable: true,
        deletable: true
      },
      {
        id: 10,
        canteenId: 4,
        name: "晚餐",
        startTime: "17:40",
        endTime: "19:10",
        switchStartTime: "14:01",
        switchEndTime: "23:00",
        enable: true,
        type: "DINNER",
        priority: 0,
        period: "17:40 - 19:10",
        switchPeriod: "14:01 - 23:00",
        editable: true,
        deletable: true
      }
    ],
    menu: {
      id: null,
      canteenId: 4,
      date: "2025-07-08",
      dayOfWeek: 2,
      online: true,
      title: "周二",
      cover: "",
      mealMenuFoods: [],
      mealTimeTypeFoods: {},
      mealFoods: [],
      mealTypeAndCategoryFoods: {
        "8": [
          {
            category: "小碗菜餐线(一、二层)",
            img: "",
            isTop: false,
            datas: [
              { img: "", subTitle: "", price: "1", name: "白粥", intro: "" },
              { img: "", subTitle: "", price: "1", name: "油条", intro: "" },
              { img: "", subTitle: "", price: "1", name: "花卷", intro: "" },
              { img: "", subTitle: "", price: "4", name: "豆浆（杯装）", intro: "" },
              { img: "", subTitle: "", price: "1", name: "冰镇酸梅汤", intro: "" },
              { img: "", subTitle: "", price: "4", name: "木耳青（杯装）", intro: "" },
              { img: "", subTitle: "", price: "1", name: "白糖桂花糕", intro: "" },
              { img: "", subTitle: "", price: "1", name: "红枣玉米", intro: "" },
              { img: "", subTitle: "", price: "7", name: "奇异果汁", intro: "" },
              { img: "", subTitle: "", price: "6", name: "海鲜小炒皇", intro: "" },
              { img: "", subTitle: "", price: "10", name: "黄豆焖猪手", intro: "" },
              { img: "", subTitle: "", price: "10", name: "肥肠百叶", intro: "" }
            ]
          },
          {
            category: "粉面档(一层)",
            img: "",
            isTop: false,
            datas: [
              { img: "", subTitle: "", price: "1", name: "油干子", intro: "" },
              { img: "", subTitle: "", price: "1.5", name: "虎皮鸡蛋(早餐)", intro: "" },
              { img: "", subTitle: "", price: "4", name: "肠粉", intro: "" },
              { img: "", subTitle: "", price: "5", name: "热干面", intro: "" },
              { img: "", subTitle: "", price: "5", name: "凉面", intro: "" },
              { img: "", subTitle: "", price: "5", name: "凉皮", intro: "" },
              { img: "", subTitle: "", price: "7", name: "炸酱面", intro: "" },
              { img: "", subTitle: "", price: "10", name: "乌鸡米线", intro: "" },
              { img: "", subTitle: "", price: "14", name: "牛肉粉面", intro: "" },
              { img: "", subTitle: "", price: "14", name: "牛杂粉面", intro: "" }
            ]
          }
        ],
        "9": [
          {
            category: "小碗菜餐线(一、二层)",
            img: "",
            isTop: false,
            datas: [
              { img: "", subTitle: "", price: "1", name: "米饭", intro: "" },
              { img: "", subTitle: "", price: "2", name: "粗粮", intro: "" },
              { img: "", subTitle: "", price: "3", name: "青菜", intro: "" },
              { img: "", subTitle: "", price: "3.5", name: "凉拌三丝", intro: "" },
              { img: "", subTitle: "", price: "4", name: "家常冬瓜", intro: "" },
              { img: "", subTitle: "", price: "4", name: "海味金针蒸水晶粉", intro: "" },
              { img: "", subTitle: "", price: "5", name: "脆笋炒腊肉", intro: "" },
              { img: "", subTitle: "", price: "6", name: "襄阳豆腐面", intro: "" },
              { img: "", subTitle: "", price: "8", name: "农家小炒肉", intro: "" },
              { img: "", subTitle: "", price: "8", name: "煎焗武昌鱼", intro: "" },
              { img: "", subTitle: "", price: "8", name: "金蒜辣汁蒸凤爪", intro: "" },
              { img: "", subTitle: "", price: "10", name: "香酥鸭", intro: "" },
              { img: "", subTitle: "", price: "10", name: "排骨面", intro: "" },
              { img: "", subTitle: "", price: "10", name: "避风塘脆皮骨", intro: "" }
            ]
          }
        ],
        "10": [
          {
            category: "小碗菜餐线(一、二层)",
            img: "",
            isTop: false,
            datas: [
              { img: "", subTitle: "", price: "1", name: "米饭", intro: "" },
              { img: "", subTitle: "", price: "2", name: "粗粮", intro: "" },
              { img: "", subTitle: "", price: "3", name: "青菜", intro: "" },
              { img: "", subTitle: "", price: "4", name: "干锅菜花", intro: "" },
              { img: "", subTitle: "", price: "4", name: "养生小炒", intro: "" },
              { img: "", subTitle: "", price: "4", name: "水煮香干", intro: "" },
              { img: "", subTitle: "", price: "6", name: "襄阳豆腐面", intro: "" },
              { img: "", subTitle: "", price: "8", name: "梅菜扣肉", intro: "" },
              { img: "", subTitle: "", price: "8", name: "风味拌鸭胗", intro: "" },
              { img: "", subTitle: "", price: "10", name: "黑椒牛仔粒", intro: "" },
              { img: "", subTitle: "", price: "10", name: "排骨面", intro: "" },
              { img: "", subTitle: "", price: "10", name: "干锅仔鸡", intro: "" }
            ]
          }
        ]
      }
    },
    slogan: "金山出品 必属佳品",
    banners: []
  }
}

// API配置
const API_CONFIG = {
  // 在生产环境中使用nginx代理，开发环境直接访问远程API
  baseURL: import.meta.env.PROD ? '/api' : 'https://zhweb.kingsoft.com',
  timeout: 10000,
  canteenCode: 'WH_000'
}

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API请求:', config.url)
    return config
  },
  (error) => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.data)
    return response
  },
  (error) => {
    console.error('响应错误:', error)
    return Promise.reject(error)
  }
)

// 数据类型定义
export interface FoodItem {
  name: string
  price: string
  img?: string
  subTitle?: string
  intro?: string
}

export interface FoodCategory {
  category: string
  img?: string
  isTop?: boolean
  datas: FoodItem[]
}

export interface TimeInterval {
  id: number
  canteenId: number
  name: string
  startTime: string
  endTime: string
  switchStartTime: string
  switchEndTime: string
  enable: boolean
  type: 'BREAKFAST' | 'LUNCH' | 'DINNER'
  priority: number
  period: string
  switchPeriod: string
  editable: boolean
  deletable: boolean
}

export interface MenuData {
  date: string
  canteenName: string
  canteenLogo?: string
  shareImgUrl?: string
  currentTimeInterval: TimeInterval
  displayDate: string
  mealTimeIntervals: TimeInterval[]
  menu: {
    id: number | null
    canteenId: number
    date: string
    dayOfWeek: number
    online: boolean
    title: string
    cover: string
    mealMenuFoods: any[]
    mealTimeTypeFoods: Record<string, any>
    mealFoods: any[]
    mealTypeAndCategoryFoods: Record<string, FoodCategory[]>
  }
  slogan?: string
  banners: any[]
}

export interface ApiResponse<T> {
  success: boolean
  msg: string
  code: number
  codeName: string
  data: T
}

// API服务类
class FoodMenuService {
  /**
   * 获取菜谱数据
   * @param date 日期 (YYYY-MM-DD格式)
   * @returns Promise<MenuData>
   */
  async getMenuData(date: string): Promise<MenuData> {
    try {
      // 首先尝试从远程API获取数据
      const response = await apiClient.get<ApiResponse<MenuData>>(
        `/mealMenu/detail/h5?canteenCode=${API_CONFIG.canteenCode}&date=${date}`
      )

      if (response.data && response.data.success && response.data.data) {
        return response.data.data
      } else {
        throw new Error('API返回数据格式错误')
      }
    } catch (error) {
      console.warn('远程API请求失败，使用本地数据:', error)

      // 如果远程API失败，返回本地数据
      return this.getLocalData(date)
    }
  }

  /**
   * 获取本地数据
   * @param date 日期
   * @returns MenuData
   */
  private getLocalData(date: string): MenuData {
    // 使用本地数据，但更新日期信息
    const data = { ...localData.data } as MenuData

    // 更新日期相关信息
    data.date = date

    // 根据日期计算显示日期
    const dateObj = new Date(date)
    const month = dateObj.getMonth() + 1
    const day = dateObj.getDate()
    data.displayDate = `${month}月${day}日`

    // 更新菜单日期
    if (data.menu) {
      data.menu.date = date
      data.menu.dayOfWeek = dateObj.getDay()

      // 根据星期几更新标题
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      data.menu.title = weekDays[dateObj.getDay()]
    }

    return data
  }

  /**
   * 根据餐次类型获取菜谱数据
   * @param date 日期
   * @param mealType 餐次类型
   * @returns Promise<FoodCategory[]>
   */
  async getMealData(date: string, mealType: 'BREAKFAST' | 'LUNCH' | 'DINNER'): Promise<FoodCategory[]> {
    const menuData = await this.getMenuData(date)

    if (!menuData.menu?.mealTypeAndCategoryFoods) {
      return []
    }

    // 查找对应餐次的时间段ID
    const timeInterval = menuData.mealTimeIntervals.find(
      interval => interval.type === mealType
    )

    if (!timeInterval) {
      return []
    }

    return menuData.menu.mealTypeAndCategoryFoods[timeInterval.id.toString()] || []
  }

  /**
   * 获取今日推荐菜品
   * @param date 日期
   * @param count 推荐数量
   * @returns Promise<FoodItem[]>
   */
  async getTodayRecommendations(date: string, count: number = 6): Promise<FoodItem[]> {
    const menuData = await this.getMenuData(date)
    const recommendations: FoodItem[] = []

    const mealData = menuData.menu?.mealTypeAndCategoryFoods
    if (mealData) {
      // 从所有餐次中收集菜品
      Object.values(mealData).forEach((categories: FoodCategory[]) => {
        categories.forEach((category) => {
          if (category.datas && category.datas.length > 0) {
            recommendations.push(...category.datas)
          }
        })
      })
    }

    // 随机选择指定数量的推荐菜品
    const shuffled = recommendations.sort(() => 0.5 - Math.random())
    return shuffled.slice(0, count)
  }

  /**
   * 获取当前应该显示的餐次
   * @returns 当前餐次信息
   */
  getCurrentMealType(): { type: 'BREAKFAST' | 'LUNCH' | 'DINNER', name: string, icon: string, route: string } {
    const hour = new Date().getHours()

    if (hour >= 6 && hour < 10) {
      return { type: 'BREAKFAST', name: '早餐', icon: '🌅', route: '/breakfast' }
    } else if (hour >= 10 && hour < 16) {
      return { type: 'LUNCH', name: '午餐', icon: '☀️', route: '/lunch' }
    } else {
      return { type: 'DINNER', name: '晚餐', icon: '🌙', route: '/dinner' }
    }
  }

  /**
   * 格式化日期为API所需格式
   * @param date Date对象或日期字符串
   * @returns YYYY-MM-DD格式的日期字符串
   */
  formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return dateObj.toISOString().split('T')[0]
  }

  /**
   * 获取今天的日期
   * @returns 今天的日期字符串
   */
  getTodayDate(): string {
    return this.formatDate(new Date())
  }
}

// 导出服务实例
export const foodMenuService = new FoodMenuService()

// 导出默认实例
export default foodMenuService
