services:
  ksfoodsearch:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ksfoodsearch-app
    ports:
      - "80:80"
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    networks:
      - ksfoodsearch-network
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ksfoodsearch-network:
    driver: bridge
