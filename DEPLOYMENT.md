# 金山食堂菜谱查询应用部署指南

## 项目概述

这是一个基于Vue 3的食堂菜谱查询应用，支持早、中、晚三餐的独立路由页面，可以按日期查询菜谱和价格。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **路由**: Vue Router 4
- **构建工具**: Vite
- **包管理器**: pnpm
- **部署**: Docker + Nginx

## 功能特性

1. **独立路由页面**:
   - 早餐页面: `/breakfast`
   - 午餐页面: `/lunch`
   - 晚餐页面: `/dinner`

2. **核心功能**:
   - 按日期查询菜谱
   - 显示菜品价格和分类
   - 响应式设计，支持移动端
   - 自动根据时间推荐当前餐次

3. **API集成**:
   - 支持远程API和本地数据备用
   - 统一的API服务管理
   - 接口请求与前端页面hostname保持一致

## 本地开发

### 环境要求

- Node.js 20+
- pnpm

### 安装依赖

```bash
pnpm install
```

### 开发模式

```bash
pnpm run dev
```

应用将在 `http://localhost:5173` 启动

### 构建生产版本

```bash
pnpm run build
```

构建产物将输出到 `dist` 目录

## Docker部署

### 方式一：使用Docker Compose（推荐）

1. 确保已安装Docker和Docker Compose

2. 构建并启动容器：
```bash
docker-compose up -d --build
```

3. 访问应用：
```
http://localhost
```

4. 停止容器：
```bash
docker-compose down
```

### 方式二：使用Docker命令

1. 构建镜像：
```bash
docker build -t ksfoodsearch .
```

2. 运行容器：
```bash
docker run -d -p 80:80 --name ksfoodsearch-app ksfoodsearch
```

3. 停止容器：
```bash
docker stop ksfoodsearch-app
docker rm ksfoodsearch-app
```

## Nginx配置说明

应用使用Nginx作为Web服务器，配置包含以下特性：

1. **静态资源优化**:
   - Gzip压缩
   - 静态资源缓存
   - 安全头设置

2. **Vue Router支持**:
   - 历史模式路由支持
   - 404页面重定向到index.html

3. **API代理**:
   - `/api/` 路径代理到远程API
   - CORS跨域支持
   - 确保接口请求与前端hostname一致

4. **健康检查**:
   - `/health` 端点用于容器健康检查

## 路由结构

```
/                 - 首页（显示当前推荐餐次和今日推荐菜品）
/breakfast        - 早餐页面（08:00-09:30）
/lunch           - 午餐页面（11:40-13:30）
/dinner          - 晚餐页面（17:40-19:10）
```

## API接口

应用支持以下数据源：

1. **远程API**: `https://zhweb.kingsoft.com/mealMenu/detail/h5`
2. **本地数据**: 当远程API不可用时自动切换到本地数据

### API参数

- `canteenCode`: 食堂代码（默认：WH_000）
- `date`: 查询日期（格式：YYYY-MM-DD）

## 环境变量

可以通过环境变量配置应用：

```bash
NODE_ENV=production    # 生产环境
```

## 监控和日志

### 健康检查

```bash
curl http://localhost/health
```

### 查看容器日志

```bash
docker-compose logs -f ksfoodsearch
```

### Nginx访问日志

容器内路径：`/var/log/nginx/access.log`

### Nginx错误日志

容器内路径：`/var/log/nginx/error.log`

## 故障排除

### 常见问题

1. **端口冲突**:
   - 修改docker-compose.yml中的端口映射
   - 或停止占用80端口的其他服务

2. **构建失败**:
   - 检查Node.js版本是否为18+
   - 清理Docker缓存：`docker system prune -a`

3. **API请求失败**:
   - 检查网络连接
   - 应用会自动降级到本地数据

### 调试模式

开发环境下可以查看详细的API请求日志：

```bash
# 查看浏览器控制台
# API请求和响应信息会在控制台显示
```

## 性能优化

1. **静态资源缓存**: 1年缓存期
2. **Gzip压缩**: 减少传输大小
3. **代码分割**: 路由级别的懒加载
4. **图片优化**: 支持现代图片格式

## 安全配置

1. **安全头**: X-Frame-Options, X-XSS-Protection等
2. **CORS配置**: 适当的跨域资源共享设置
3. **内容安全策略**: CSP头防止XSS攻击

## 扩展和定制

### 添加新的餐次

1. 在API服务中添加新的餐次类型
2. 创建对应的Vue组件
3. 在路由中添加新路径
4. 更新导航组件

### 修改样式主题

主要样式文件位于各组件的`<style>`部分，使用CSS变量可以方便地进行主题定制。

### 添加新功能

1. 在`src/services/api.ts`中添加新的API方法
2. 创建相应的Vue组件
3. 更新路由和导航

## 维护

### 更新依赖

```bash
pnpm update
```

### 重新构建镜像

```bash
docker-compose build --no-cache
```

### 备份数据

本应用主要使用远程API，本地只有示例数据文件`data.json`作为备用。

## 支持

如有问题，请检查：

1. Docker和Docker Compose版本
2. 网络连接状态
3. 端口占用情况
4. 容器日志信息
