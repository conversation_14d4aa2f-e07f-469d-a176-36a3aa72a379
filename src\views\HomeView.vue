<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { RouterLink } from 'vue-router'
import { foodMenuService, type MenuData, type FoodItem } from '../services/api'

const currentTime = ref(new Date())
const foodData = ref<MenuData | null>(null)
const todayRecommendations = ref<FoodItem[]>([])
const loading = ref(false)

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date()
}

// 获取当前应该显示的餐次
const getCurrentMeal = computed(() => {
  return foodMenuService.getCurrentMealType()
})

// 加载今日数据
const loadTodayData = async () => {
  loading.value = true
  try {
    const today = foodMenuService.getTodayDate()

    // 并行加载菜谱数据和推荐菜品
    const [menuData, recommendations] = await Promise.all([
      foodMenuService.getMenuData(today),
      foodMenuService.getTodayRecommendations(today, 6)
    ])

    foodData.value = menuData
    todayRecommendations.value = recommendations
  } catch (error) {
    console.error('加载今日数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  updateTime()
  loadTodayData()

  // 每分钟更新一次时间
  setInterval(updateTime, 60000)
})
</script>

<template>
  <div class="home-page">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-banner">
      <p>正在加载今日菜谱...</p>
    </div>

    <!-- 欢迎横幅 -->
    <section v-else class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1 class="welcome-title">欢迎来到金山食堂</h1>
          <p class="welcome-subtitle">{{ foodData?.displayDate || '今日' }} · {{ getCurrentMeal.name }}时间</p>
          <p class="canteen-slogan">{{ foodData?.slogan || '金山出品 必属佳品' }}</p>
        </div>
        <div class="current-meal-card">
          <div class="meal-icon">{{ getCurrentMeal.icon }}</div>
          <div class="meal-info">
            <h3>当前推荐</h3>
            <p>{{ getCurrentMeal.name }}</p>
            <RouterLink :to="getCurrentMeal.route" class="meal-link">
              立即查看 →
            </RouterLink>
          </div>
        </div>
      </div>
    </section>

    <!-- 快速导航 -->
    <section class="quick-nav">
      <h2 class="section-title">选择用餐时间</h2>
      <div class="nav-cards">
        <RouterLink to="/breakfast" class="nav-card breakfast-card">
          <div class="card-icon">🌅</div>
          <h3>早餐</h3>
          <p>08:00 - 09:30</p>
          <span class="card-arrow">→</span>
        </RouterLink>

        <RouterLink to="/lunch" class="nav-card lunch-card">
          <div class="card-icon">☀️</div>
          <h3>午餐</h3>
          <p>11:40 - 13:30</p>
          <span class="card-arrow">→</span>
        </RouterLink>

        <RouterLink to="/dinner" class="nav-card dinner-card">
          <div class="card-icon">🌙</div>
          <h3>晚餐</h3>
          <p>17:40 - 19:10</p>
          <span class="card-arrow">→</span>
        </RouterLink>
      </div>
    </section>

    <!-- 今日推荐 -->
    <section class="recommendations" v-if="todayRecommendations.length > 0">
      <h2 class="section-title">今日推荐</h2>
      <div class="recommendation-grid">
        <div
          v-for="(food, index) in todayRecommendations"
          :key="index"
          class="recommendation-item"
        >
          <div class="food-name">{{ food.name }}</div>
          <div class="food-price">¥{{ food.price }}</div>
        </div>
      </div>
    </section>

    <!-- 食堂信息 -->
    <section class="canteen-info">
      <div class="info-card">
        <h2>{{ foodData?.canteenName || '武汉金山软件园食堂' }}</h2>
        <div class="info-details">
          <div class="info-item">
            <span class="info-icon">📍</span>
            <span>武汉金山软件园</span>
          </div>
          <div class="info-item">
            <span class="info-icon">🕒</span>
            <span>营业时间：06:00 - 23:00</span>
          </div>
          <div class="info-item">
            <span class="info-icon">📞</span>
            <span>服务热线：400-123-4567</span>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.loading-banner {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 60px 20px;
  text-align: center;
  color: white;
  font-size: 18px;
  margin-bottom: 30px;
}

.welcome-banner {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 40px 20px;
  margin-bottom: 30px;
}

.banner-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  align-items: center;
}

.welcome-text {
  color: white;
}

.welcome-title {
  font-size: 48px;
  font-weight: bold;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #fff, #f1c40f);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 20px;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.canteen-slogan {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
  font-style: italic;
}

.current-meal-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transform: translateY(-10px);
}

.meal-icon {
  font-size: 60px;
  margin-bottom: 15px;
}

.meal-info h3 {
  margin: 0 0 5px 0;
  color: #2d3436;
  font-size: 18px;
}

.meal-info p {
  margin: 0 0 15px 0;
  color: #636e72;
  font-size: 24px;
  font-weight: bold;
}

.meal-link {
  display: inline-block;
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 10px 20px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 500;
  transition: transform 0.2s ease;
}

.meal-link:hover {
  transform: scale(1.05);
}

.quick-nav, .recommendations, .canteen-info {
  max-width: 1200px;
  margin: 0 auto 30px auto;
  padding: 0 20px;
}

.section-title {
  color: white;
  font-size: 28px;
  text-align: center;
  margin-bottom: 30px;
  font-weight: bold;
}

.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.nav-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  text-decoration: none;
  color: #2d3436;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s;
}

.nav-card:hover::before {
  left: 100%;
}

.nav-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.breakfast-card:hover {
  background: linear-gradient(135deg, #ffeaa7, #fdcb6e);
}

.lunch-card:hover {
  background: linear-gradient(135deg, #a8e6cf, #7fcdcd);
}

.dinner-card:hover {
  background: linear-gradient(135deg, #d1a3ff, #a29bfe);
}

.card-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.nav-card h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: bold;
}

.nav-card p {
  margin: 0 0 15px 0;
  color: #636e72;
  font-size: 16px;
}

.card-arrow {
  font-size: 20px;
  font-weight: bold;
  color: #667eea;
}

.recommendation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.recommendation-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  transition: transform 0.2s ease;
}

.recommendation-item:hover {
  transform: scale(1.05);
}

.food-name {
  font-weight: bold;
  margin-bottom: 8px;
  color: #2d3436;
}

.food-price {
  color: #e17055;
  font-size: 18px;
  font-weight: bold;
}

.info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
}

.info-card h2 {
  margin: 0 0 20px 0;
  color: #2d3436;
  font-size: 24px;
}

.info-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #636e72;
}

.info-icon {
  font-size: 18px;
}

@media (max-width: 768px) {
  .banner-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .welcome-title {
    font-size: 36px;
  }

  .nav-cards {
    grid-template-columns: 1fr;
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .info-details {
    align-items: center;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 28px;
  }

  .section-title {
    font-size: 24px;
  }

  .nav-card {
    padding: 20px;
  }

  .card-icon {
    font-size: 36px;
  }
}
</style>
